import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';

/**
 * A decorator that prevents GraphQL field resolution beyond a specified depth
 * to avoid circular dependencies and infinite loops.
 *
 * @param maxDepth - The maximum number of times this field can appear in the resolution path
 * @returns boolean - true if the field can be resolved, false if depth limit is exceeded
 */
export const FieldDepthLimit = createParamDecorator((maxDepth: number, context: ExecutionContext): boolean => {
	const ctx = GqlExecutionContext.create(context);
	const info = ctx.getInfo();

	if (!info || !info.path) {
		// If we can't determine the path, allow resolution to be safe
		return true;
	}

	const fieldName = info.fieldName;
	let current = info.path;
	let fieldCount = 0;

	// Count occurrences of this field in the resolution path
	while (current) {
		if (current.key === fieldName) {
			fieldCount++;
		}
		current = current.prev;
	}

	// Allow resolution if we haven't exceeded the maximum depth
	return fieldCount <= maxDepth;
});
