export const enum CacheScopes {
	Event = 'event',
}

export const enum CacheCategories {
	Athlete = 'athlete',
	TeamsAthletes = 'athlete:team',
	Staff = 'staff',
	Club = 'club',
	Court = 'court',
	Division = 'div',
	DivisionRounds = 'div:round',
	DivisionMedia = 'div:media',
	PoolBracket = 'pb', // PoolBracket (pb_*)
	RoundPoolBrackets = 'pb:round',
	DivisionPoolBrackets = 'pb:div',
	MatchesPoolBracketsShortInfo = 'pbi:match',
	TeamOngoingPoolBracket = 'pb:team_ongoing',
	Team = 'team', // Team (team_*)
	ClubTeams = 'team:club',
	DivisionTeams = 'team:div',
	QualifiedTeams = 'team:qlf',
	PoolBracketTeams = 'team:pb',
	DivisionStanding = 'standing', // DivisionStanding (standing_*)
	TeamsStandings = 'standing:team',
	Match = 'match', // Match (match_*)
	TeamsMatches = 'match:team',
	TeamsUpcomingMatches = 'match:team_upcoming',
	TeamsFinishedMatches = 'match:team_finished',
	TeamsNextMatch = 'match:next',
	PoolBracketMatches = 'match:pb',
	DayMatch = 'match:day',
	DivisionMatchTimeRange = 'match:div_range',
	Official = 'official',
	HeadOfficial = 'official:head',
	//
	Page = 'page',
	Event = 'event',
}

export const enum CacheCommonKeys {
	All = 'all',
}
