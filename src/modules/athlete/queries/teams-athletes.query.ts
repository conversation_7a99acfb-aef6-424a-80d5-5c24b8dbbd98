import { ENTRY_STATUSES } from '@/shared/constants/team';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type TeamsAthletesQueryParams = {
	eventId: string;
	teamsIds: string[];
};

export const getTeamsAthletesQuery = ({ eventId, teamsIds }: TeamsAthletesQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const teamsIdsValues = teamsIds.map((id) => sqlVars.useValue(Number(id)));
	const query = `
		SELECT 
			ra.roster_athlete_id AS athlete_id,
			ma.first,
			ma.last,
			rc.club_name,
			rc.state,
			rt.roster_team_id AS team_id,
			rt.team_name,
			rt.organization_code,
			sp.short_name AS short_position,
			(
				CASE
					WHEN e.sport_sanctioning_id = 1 THEN COALESCE(ra.aau_jersey, ma.aau_jersey)
					ELSE COALESCE(ra.jersey, ma.jersey)
				END
			) as uniform
		FROM event e
		JOIN roster_club rc
			ON rc.event_id = e.event_id
			AND rc.deleted is NULL
		JOIN roster_team rt 
			ON rt.roster_club_id = rc.roster_club_id
			AND rt.event_id = e.event_id
			AND rt.status_entry = ${ENTRY_STATUSES.ACCEPTED}
			AND rt.deleted IS NULL
		JOIN division d
			ON d.division_id = rt.division_id
			AND d.event_id = e.event_id
			AND d.published is TRUE
		JOIN roster_athlete ra
			ON ra.roster_team_id = rt.roster_team_id
			AND ra.event_id = e.event_id
			AND ra.deleted IS NULL
			AND ra.deleted_by_user IS NULL
			AND (ra.as_staff = 0 OR ra.as_staff IS NULL)
		JOIN master_athlete ma 
			ON ma.master_athlete_id = ra.master_athlete_id
			AND ma.deleted is NULL
		LEFT JOIN sport_position sp
			ON sp.sport_position_id = COALESCE(ra.sport_position_id, ma.sport_position_id)
		WHERE
			e.event_id = ${sqlVars.useValue(Number(eventId))}
			AND e.deleted IS NULL
			AND rt.roster_team_id IN (${teamsIdsValues.join(',')})
    ORDER BY
			rt.team_name,
			CASE
				WHEN e.sport_sanctioning_id = 1 THEN COALESCE(ra.aau_jersey, ma.aau_jersey, 0)
				ELSE COALESCE(ra.jersey, ma.jersey, 0)
			END,
			ma.last,
			ma.first
	`;

	return [query, sqlVars.getValues()];
};
