import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { castFieldsToString, StringCastKeys } from '@/shared/utils/format';
import { PageParams, PageResults, toOffsetLimit } from '@/shared/utils/pagination';

import { Athlete } from './entities';
import {
	getAthletesQuery,
	getPaginatedAthletesQuery,
	getTeamsAthletesQuery,
	TeamsAthletesQueryParams,
	AthletesQueryParams,
	PaginatedAthletesQueryParams,
} from './queries';

export type FetchAthletesParams = AthletesQueryParams;
export type FetchTeamsAthletesParams = TeamsAthletesQueryParams;
export type FetchPaginatedAthletesParams = Omit<PaginatedAthletesQueryParams, 'offset' | 'limit'> & PageParams;

const ATHLETE_STRING_CAST_FIELDS: StringCastKeys<Athlete>[] = ['athlete_id', 'team_id'];

@Injectable()
export class AthleteRepository {
	constructor(private prisma: PrismaService) {}

	async fetchPaginatedAthletes(params: FetchPaginatedAthletesParams): Promise<PageResults<Athlete>> {
		const [query, values] = getPaginatedAthletesQuery({ ...params, ...toOffsetLimit(params) });

		const items = await this.prisma.$queryRawUnsafe<Athlete[]>(query, ...values);
		if (!items.length) {
			return { items, itemCount: 0 };
		}
		// Get the item count from the first row as it is the same for all rows
		return {
			items: this._formatAthletesFields(items),
			itemCount: Number((items[0] as unknown as Record<string, string>).item_count),
		};
	}

	async fetchAthletes(params: FetchAthletesParams): Promise<Athlete[]> {
		const [query, values] = getAthletesQuery(params);
		const items = await this.prisma.$queryRawUnsafe<Athlete[]>(query, ...values);
		return this._formatAthletesFields(items);
	}

	async fetchTeamsAthletes(params: FetchTeamsAthletesParams): Promise<Athlete[]> {
		const [query, values] = getTeamsAthletesQuery(params);
		const items = await this.prisma.$queryRawUnsafe<Athlete[]>(query, ...values);
		return this._formatAthletesFields(items);
	}

	private _formatAthletesFields(athletes: Athlete[]): Athlete[] {
		return castFieldsToString(athletes, ATHLETE_STRING_CAST_FIELDS);
	}
}
