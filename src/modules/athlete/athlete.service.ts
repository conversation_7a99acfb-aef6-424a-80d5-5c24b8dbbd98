import { Injectable } from '@nestjs/common';

import { ExtendedCacheService, ItemCacheKeyConfig, ItemsCacheKeyConfig } from '@/infra/cache/extended-cache.service';
import { CacheCategories, CacheScopes } from '@/shared/constants/cache';
import { groupBy, keyBy } from '@/shared/utils/collection';
import { EventKey } from '@/shared/utils/event';
import { completeObject } from '@/shared/utils/object';
import { createPageInfo } from '@/shared/utils/pagination';

import { AthleteSearchService, FindPaginatedAthletesIdsParams } from './athlete-search.service';
import {
	AthleteRepository,
	FetchPaginatedAthletesParams as PaginatedAthletesParams,
	FetchAthletesParams,
	FetchTeamsAthletesParams,
} from './athlete.repository';
import { Athlete, PaginatedAthletes } from './entities';

export type FetchPaginatedAthletesParams = PaginatedAthletesParams; // ?

@Injectable()
export class AthleteService {
	constructor(
		private athleteRepository: AthleteRepository,
		private athleteSearchService: AthleteSearchService,
		private cacheService: ExtendedCacheService,
	) {}

	async fetchPaginatedAthletes(params: FetchPaginatedAthletesParams): Promise<PaginatedAthletes> {
		if (this.athleteSearchService.canSearchWith(params)) {
			return this._findPaginatedAthletes(params);
		} else {
			return this.cacheService.getOrFetch<PaginatedAthletes>(this._getPaginatedAthletesCacheKeyConfig(params), async () =>
				this._fetchPaginatedAthletesData(params),
			);
		}
	}

	async fetchTeamsAthletes(params: FetchTeamsAthletesParams): Promise<Record<string, Athlete[]>> {
		const cacheKeyConfig = this._getTeamsAthletesCacheKeyConfig(params);
		return this.cacheService.getOrFetchMany<Athlete[]>(cacheKeyConfig, params.teamsIds, (missingIds) =>
			this._getTeamsAthletesData({ ...params, teamsIds: missingIds }),
		);
	}

	private async _fetchPaginatedAthletesData(params: FetchPaginatedAthletesParams): Promise<PaginatedAthletes> {
		const { items, itemCount } = await this.athleteRepository.fetchPaginatedAthletes(params);
		return {
			items,
			page_info: createPageInfo(params, itemCount),
		};
	}

	private async _getAthletesData(params: FetchAthletesParams): Promise<Record<string, Athlete | null>> {
		const athletes = await this.athleteRepository.fetchAthletes(params);
		const athletesMap = keyBy(athletes, 'athlete_id');
		return completeObject(params.athletesIds, athletesMap, null);
	}

	private async _getTeamsAthletesData(params: FetchTeamsAthletesParams): Promise<Record<string, Athlete[]>> {
		const athletes = await this.athleteRepository.fetchTeamsAthletes(params);
		const athletesMap = groupBy(athletes, 'team_id');
		return completeObject(params.teamsIds, athletesMap, []);
	}

	private async _findPaginatedAthletes(params: FindPaginatedAthletesIdsParams): Promise<PaginatedAthletes> {
		const { eventKey } = params;
		const { items: athletesIds, itemCount } = await this.athleteSearchService.findPaginatedAthletesIds(params);
		const athletes = await this.cacheService.getOrFetchMany(this._getAthletesCacheKeyConfig(eventKey), athletesIds, (missingIds) =>
			this._getAthletesData({ eventKey, athletesIds: missingIds }),
		);
		return {
			items: athletesIds.map((id) => athletes[id]).filter(Boolean),
			page_info: createPageInfo(params, itemCount),
		};
	}

	private _getPaginatedAthletesCacheKeyConfig(params: FetchPaginatedAthletesParams): ItemCacheKeyConfig | null {
		if (params.search?.trim()) return null; // Do not cache search results

		const { eventKey, page, pageSize } = params;
		return {
			scope: CacheScopes.Event,
			scopeKey: eventKey.value,
			category: CacheCategories.Athlete,
			categoryKey: `${page}-${pageSize}`,
		};
	}

	private _getAthletesCacheKeyConfig(eventKey: EventKey): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: eventKey.value,
			category: CacheCategories.Athlete,
		};
	}

	private _getTeamsAthletesCacheKeyConfig(params: FetchTeamsAthletesParams): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventId,
			category: CacheCategories.TeamsAthletes,
		};
	}
}
