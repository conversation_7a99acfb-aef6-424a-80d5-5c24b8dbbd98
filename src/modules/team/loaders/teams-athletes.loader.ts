import { Injectable } from '@nestjs/common';
import * as DataLoader from 'dataloader';

import { AthleteService } from '@/modules/athlete/athlete.service';
import { Athlete } from '@/modules/athlete/entities';
import { Team } from '@/modules/team/entities';

@Injectable()
export class TeamsAthletesLoader {
	constructor(private athleteService: AthleteService) {}

	create(eventId: string): DataLoader<Team, Athlete[]> {
		return new DataLoader<Team, Athlete[]>(
			async (teams: Team[]) => {
				const teamsIds = teams.map((team) => team.team_id);
				const teamsAthletesMap = await this.athleteService.fetchTeamsAthletes({ eventId, teamsIds });
				return teamsIds.map((teamId) => teamsAthletesMap[teamId]);
			},
			{ cache: false, name: 'TeamsAthletesLoader' },
		);
	}
}
