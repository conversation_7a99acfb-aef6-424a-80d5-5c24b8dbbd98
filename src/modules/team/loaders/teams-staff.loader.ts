import { Injectable } from '@nestjs/common';
import * as DataLoader from 'dataloader';

import { Staff } from '@/modules/staff/entities';
import { StaffService } from '@/modules/staff/staff.service';
import { Team } from '@/modules/team/entities';

@Injectable()
export class TeamsStaffLoader {
	constructor(private staffService: StaffService) {}

	create(eventId: string): DataLoader<Team, Staff[]> {
		return new DataLoader<Team, Staff[]>(
			async (teams: Team[]) => {
				const teamsIds = teams.map((team) => team.team_id);
				const teamsStaffMap = await this.staffService.fetchTeamsStaff({ eventId, teamsIds });
				return teamsIds.map((teamId) => teamsStaffMap[teamId]);
			},
			{ cache: false, name: 'TeamsStaffLoader' },
		);
	}
}
