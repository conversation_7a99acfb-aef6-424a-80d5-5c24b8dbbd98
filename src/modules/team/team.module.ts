import { Module, forwardRef } from '@nestjs/common';

import { CacheModule } from '@/infra/cache/cache.module';
import { EventHelperModule } from '@/infra/eventHelper/eventHelper.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';

import { TEAMS_INDEX_SCHEMA } from './constants';
import {
	TeamsDivisionStandingLoader,
	TeamsMatchesLoader,
	TeamsNextMatchLoader,
	TeamsFinishedMatchesLoader,
	TeamsUpcomingMatchesLoader,
	TeamsOngoingPoolBracketsLoader,
	TeamsAthletesLoader,
	TeamsStaffLoader,
} from './loaders';
import { TeamSearchRepository } from './team-search.repository';
import { TeamSearchService } from './team-search.service';
import { TeamRepository } from './team.repository';
import { TeamResolver } from './team.resolver';
import { TeamService } from './team.service';
import { AthleteModule } from '../athlete/athlete.module';
import { DivisionStandingModule } from '../divisionStanding/division-standing.module';
import { MatchModule } from '../match/match.module';
import { PoolBracketModule } from '../poolBracket/pool-bracket.module';
import { SearchModule } from '../search/search.module';
import { SearchEntityName } from '../search/types';
import { StaffModule } from '../staff/staff.module';

@Module({
	imports: [
		PrismaModule,
		CacheModule,
		AthleteModule,
		StaffModule,
		MatchModule,
		forwardRef(() => PoolBracketModule),
		DivisionStandingModule,
		EventHelperModule,
		SearchModule.forFeature(SearchEntityName.Teams, TEAMS_INDEX_SCHEMA),
	],
	providers: [
		TeamResolver,
		TeamService,
		TeamRepository,
		TeamSearchRepository,
		TeamSearchService,
		TeamsAthletesLoader,
		TeamsStaffLoader,
		TeamsMatchesLoader,
		TeamsNextMatchLoader,
		TeamsDivisionStandingLoader,
		TeamsFinishedMatchesLoader,
		TeamsUpcomingMatchesLoader,
		TeamsOngoingPoolBracketsLoader,
	],
	exports: [TeamService],
})
export class TeamModule {}
