import { ENTRY_STATUSES } from '@/shared/constants/team';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type TeamsStaffQueryParams = {
	eventId: string;
	teamsIds: string[];
};

export const getTeamsStaffQuery = ({ eventId, teamsIds }: TeamsStaffQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const teamsIdsValues = teamsIds.map((id) => sqlVars.useValue(Number(id)));
	const query = `
		SELECT 
			rsr.roster_staff_role_id as staff_id,
			ms.first,
			ms.last,
			rc.club_name,
			rc.state,
			rt.roster_team_id AS team_id,
			rt.team_name,
			rt.organization_code,
			r.name AS role_name
		FROM event e
		JOIN roster_club rc
			ON rc.event_id = e.event_id
			AND rc.deleted is NULL
		JOIN roster_team rt 
			ON rt.roster_club_id = rc.roster_club_id
			AND rt.event_id = e.event_id
			AND rt.status_entry = ${ENTRY_STATUSES.ACCEPTED}
			AND rt.deleted IS NULL
		JOIN division d
			ON d.division_id = rt.division_id
			AND d.event_id = e.event_id
			AND d.published is TRUE
		JOIN roster_staff_role rsr
			ON rsr.roster_team_id = rt.roster_team_id
			AND rsr.deleted IS NULL
			AND rsr.deleted_by_user IS NULL
		JOIN master_staff ms
			ON ms.master_staff_id = rsr.master_staff_id
			AND ms.deleted is NULL
		LEFT JOIN master_staff_role msr 
			ON msr.master_staff_id = rsr.master_staff_id
			AND msr.master_team_id = rsr.master_team_id
		LEFT JOIN role r
			ON r.role_id = COALESCE(NULLIF(rsr.role_id, '0'), msr.role_id)
		WHERE
			e.event_id = ${sqlVars.useValue(Number(eventId))}
			AND e.deleted IS NULL
			AND rt.roster_team_id IN (${teamsIdsValues.join(',')})
		ORDER BY
			rt.team_name,
			r.sort_order NULLS LAST,
			ms.last,
			ms.first
	`;

	return [query, sqlVars.getValues()];
};
