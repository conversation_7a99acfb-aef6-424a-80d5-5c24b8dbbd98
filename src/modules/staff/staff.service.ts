import { Injectable } from '@nestjs/common';

import { ExtendedCacheService, ItemCacheKeyConfig, ItemsCacheKeyConfig } from '@/infra/cache/extended-cache.service';
import { CacheCategories, CacheScopes } from '@/shared/constants/cache';
import { groupBy, keyBy } from '@/shared/utils/collection';
import { EventKey } from '@/shared/utils/event';
import { completeObject } from '@/shared/utils/object';
import { createPageInfo } from '@/shared/utils/pagination';

import { PaginatedStaff, Staff } from './entities';
import { FindPaginatedStaffIdsParams, StaffSearchService } from './staff-search.service';
import {
	StaffRepository,
	FetchPaginatedStaffParams as PaginatedStaffParams,
	FetchStaffParams,
	FetchTeamsStaffParams,
} from './staff.repository';

export type FetchPaginatedStaffParams = PaginatedStaffParams;

@Injectable()
export class StaffService {
	constructor(
		private staffRepository: StaffRepository,
		private staffSearchService: StaffSearchService,
		private cacheService: ExtendedCacheService,
	) {}

	async fetchPaginatedStaff(params: FetchPaginatedStaffParams): Promise<PaginatedStaff> {
		if (this.staffSearchService.canSearchWith(params)) {
			return this._findPaginatedStaff(params);
		} else {
			return this.cacheService.getOrFetch<PaginatedStaff>(this._getPaginatedStaffCacheKeyConfig(params), async () =>
				this._fetchPaginatedStaffData(params),
			);
		}
	}

	async fetchTeamsStaff(params: FetchTeamsStaffParams): Promise<Record<string, Staff[]>> {
		const cacheKeyConfig = this._getTeamsStaffCacheKeyConfig(params);
		return this.cacheService.getOrFetchMany<Staff[]>(cacheKeyConfig, params.teamsIds, (missingIds) =>
			this._getTeamsStaffData({ ...params, teamsIds: missingIds }),
		);
	}

	private async _fetchPaginatedStaffData(params: FetchPaginatedStaffParams): Promise<PaginatedStaff> {
		const { items, itemCount } = await this.staffRepository.fetchPaginatedStaff(params);
		return {
			items,
			page_info: createPageInfo(params, itemCount),
		};
	}

	private async _getStaffData(params: FetchStaffParams): Promise<Record<string, Staff | null>> {
		const staff = await this.staffRepository.fetchStaff(params);
		const staffMap = keyBy(staff, 'staff_id');
		return completeObject(params.staffIds, staffMap, null);
	}

	private async _getTeamsStaffData(params: FetchTeamsStaffParams): Promise<Record<string, Staff[]>> {
		const staff = await this.staffRepository.fetchTeamsStaff(params);
		const staffMap = groupBy(staff, 'team_id');
		return completeObject(params.teamsIds, staffMap, []);
	}

	private async _findPaginatedStaff(params: FindPaginatedStaffIdsParams): Promise<PaginatedStaff> {
		const { eventKey } = params;
		const { items: staffIds, itemCount } = await this.staffSearchService.findPaginatedStaffIds(params);
		const staff = await this.cacheService.getOrFetchMany(this._getStaffCacheKeyConfig(eventKey), staffIds, (missingIds) =>
			this._getStaffData({ eventKey, staffIds: missingIds }),
		);
		return {
			items: staffIds.map((id) => staff[id]).filter(Boolean),
			page_info: createPageInfo(params, itemCount),
		};
	}

	private _getPaginatedStaffCacheKeyConfig(params: FetchPaginatedStaffParams): ItemCacheKeyConfig | null {
		if (params.search?.trim()) return null;

		const { eventKey, page, pageSize } = params;
		return {
			scope: CacheScopes.Event,
			scopeKey: eventKey.value,
			category: CacheCategories.Staff,
			categoryKey: `${page}-${pageSize}`,
		};
	}

	private _getStaffCacheKeyConfig(eventKey: EventKey): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: eventKey.value,
			category: CacheCategories.Staff,
		};
	}

	private _getTeamsStaffCacheKeyConfig(params: FetchTeamsStaffParams): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventId,
			category: CacheCategories.TeamsStaff,
		};
	}
}
