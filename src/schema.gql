# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Athlete {
  athlete_id: ID!
  first: String!
  last: String!
  club_name: String!
  team_id: ID!
  team_name: String!
  organization_code: String
  state: String
  short_position: String
  uniform: String
}

type PageInfo {
  page: Float!
  page_size: Float!
  page_count: Float!
  item_count: Float!
}

type PaginatedAthletes {
  items: [Athlete!]!
  page_info: PageInfo!
}

type ClubShortInfo {
  roster_club_id: ID!
  club_name: String!
  state: String
  club_code: String!
}

type Club {
  roster_club_id: ID!
  club_name: String!
  state: String
  club_code: String!
  teams_count: Int!
  teams: [Team!]!
}

type PaginatedClubs {
  items: [Club!]!
  page_info: PageInfo!
}

type TeamShortInfo {
  team_id: ID!
  team_name: String!
  master_team_id: ID
}

type TeamExtra {
  prev_qual: Boolean
  prev_qual_age: String
  prev_qual_division: String
  show_accepted_bid: Boolean
  show_previously_accepted_bid: String
  earned_at: String
}

type TeamExternal {
  club_info: ClubShortInfo!
}

type Team {
  team_id: ID!
  team_name: String!
  master_team_id: ID
  club_id: ID!
  team_code: String
  division_id: ID!
  division_name: String
  extra: TeamExtra
  external: TeamExternal!
  manual_club_name: String
  athletes: [Athlete!]!
  matches(pb_only: Boolean, as_team: Boolean, as_ref: Boolean, limit: Int): [Match!]!
  upcoming_matches(pb_only: Boolean, as_team: Boolean, as_ref: Boolean, limit: Int): [Match!]!
  finished_matches(pb_only: Boolean, as_team: Boolean, as_ref: Boolean, limit: Int): [Match!]!
  next_match: Match
  division_standing: DivisionStanding
  pool_bracket_stat: PoolOrBracketStatItem
  ongoing_pool_bracket: PoolOrBracket
}

union PoolOrBracket = Pool | Bracket

type Pool implements PoolOrBracketBase {
  is_pool: Float!
  uuid: ID!
  round_id: ID!
  group_id: ID
  division_id: ID!
  sort_priority: Float!
  team_count: Float!
  match_count: Float!
  name: String
  short_name: String
  display_name: String
  court_short_name: String
  date_start: Float
  consolation: Float
  pb_stats: [PoolOrBracketStatItem!]!
  pb_seeds: [PoolOrBracketSeedItem!]!
  pb_finishes: [TeamAdvancement!]!
  settings: PoolOrBracketSettings
  external: PoolOrBracketExternal!
  teams(filterTeamsIds: [ID!]): [Team!]!
  matches: [Match!]!
  team_pb_stat: PoolOrBracketStatItem
}

interface PoolOrBracketBase {
  is_pool: Float!
  uuid: ID!
  round_id: ID!
  group_id: ID
  division_id: ID!
  sort_priority: Float!
  team_count: Float!
  match_count: Float!
  name: String
  short_name: String
  display_name: String
  court_short_name: String
  date_start: Float
  consolation: Float
  pb_stats: [PoolOrBracketStatItem!]!
  pb_seeds: [PoolOrBracketSeedItem!]!
  pb_finishes: [TeamAdvancement!]!
  settings: PoolOrBracketSettings
  external: PoolOrBracketExternal!
  teams(filterTeamsIds: [ID!]): [Team!]!
  matches: [Match!]!
  team_pb_stat: PoolOrBracketStatItem
}

type Bracket implements PoolOrBracketBase {
  is_pool: Float!
  uuid: ID!
  round_id: ID!
  group_id: ID
  division_id: ID!
  sort_priority: Float!
  team_count: Float!
  match_count: Float!
  name: String
  short_name: String
  display_name: String
  court_short_name: String
  date_start: Float
  consolation: Float
  pb_stats: [PoolOrBracketStatItem!]!
  pb_seeds: [PoolOrBracketSeedItem!]!
  pb_finishes: [TeamAdvancement!]!
  settings: PoolOrBracketSettings
  external: PoolOrBracketExternal!
  teams(filterTeamsIds: [ID!]): [Team!]!
  matches: [Match!]!
  team_pb_stat: PoolOrBracketStatItem
  flow_chart: String
}

type PaginatedTeams {
  items: [Team!]!
  page_info: PageInfo!
}

type DivisionStanding {
  division_standing_id: ID!
  team_id: ID!
  division_id: ID!
  matches_won: Float!
  matches_lost: Float!
  sets_won: Float!
  sets_lost: Float!
  sets_pct: Float!
  points_ratio: Float!
  rank: Float!
  seed: Float
  seed_original: Float
  points: Float
  heading: String!
  heading_priority: Float!
}

type FilterInfo {
  total_items: Float!
  filtered_items: Float!
  is_filtered: Boolean!
}

type CourtShortInfo {
  uuid: ID!
  short_name: String
}

type Court {
  uuid: ID!
  short_name: String
  name: String!
  sort_priority: Float
}

type MatchReference {
  court: String
  match_id: String
  week_day: String
  secs_start: Float
  display_name: String
  start_time_string: String
  external: MatchReferenceExternal
}

type TeamAdvancement {
  team_name: String
  team_id: Float
  next_match: MatchReference
  next_ref: MatchReference
}

type PoolOrBracketSeedItem {
  id: ID
  name: String
  type: Float
  seed: Float
  overallSeed: Float
  reseedRank: Float
}

type PoolOrBracketSettings {
  DoubleRR: Boolean
  Duration: Float
  NoWinner: Boolean
  SetCount: Float
  FinalPoints: Float
  PlayAllSets: Boolean
  WinningPoints: Float
}

type PoolOrBracketShortInfo implements PoolOrBracketShortInfoInterface {
  is_pool: Float!
  uuid: ID!
}

interface PoolOrBracketShortInfoInterface {
  is_pool: Float!
  uuid: ID!
}

type PoolOrBracketStatItem {
  team_id: ID
  name: String
  rank: Float
  sets_pct: Float
  sets_won: Float
  sets_lost: Float
  points_won: Float
  matches_pct: Float
  matches_won: Float
  points_lost: Float
  matches_lost: Float
  points_ratio: Float
}

type PoolOrBracketExternal {
  courts_short_info: [CourtShortInfo!]!
}

type MatchExternal {
  pool_bracket_info: PoolOrBracketShortInfo!
  team_pb_seed: PoolOrBracketSeedItem
  opponent_pb_seed: PoolOrBracketSeedItem
  ref_team_pb_seed: PoolOrBracketSeedItem
  court_info: CourtShortInfo!
  team_info: TeamShortInfo
  opponent_info: TeamShortInfo
  ref_team_info: TeamShortInfo
  team_display_name: String!
  opponent_display_name: String!
  ref_team_display_name: String!
  has_finishes: Boolean!
}

type MatchFinishes {
  winner: TeamAdvancement
  looser: TeamAdvancement
}

type MatchTeamResults {
  scores: String
  roster_team_id: String
}

type MatchResults {
  sets: [String!]!
  team1: MatchTeamResults
  team2: MatchTeamResults
  winner: String
  winner_team: MatchTeamResults
}

type MatchSourceItem {
  id: ID
  name: String
  type: Float
  seed: Float
  overallSeed: Float
  reseedRank: Float
}

type MatchSource {
  team: MatchSourceItem
  opponent: MatchSourceItem
  ref: MatchSourceItem
}

type Match {
  team_id: ID
  opponent_id: ID
  ref_team_id: ID
  match_id: ID!
  pool_bracket_id: ID!
  court_id: ID!
  match_name: String
  match_number: Float
  division_id: ID!
  division_name: String
  secs_start: Float
  secs_end: Float
  secs_finished: Float
  is_tb: Float
  source: MatchSource
  results: MatchResults
  finishes: MatchFinishes
  external: MatchExternal!
}

type FilteredMatches {
  items: [Match!]!
  filter_info: FilterInfo!
}

type MatchesTimeRange {
  division_id: ID!
  day: String!
  start_time: String!
  end_time: String!
}

type Round {
  uuid: ID!
  division_id: ID!
  sort_priority: Float!
  name: String
  short_name: String
  first_match_start: Float
  last_match_start: Float
  pool_brackets: [PoolOrBracket!]!
}

type EventMedia {
  media_id: ID!
  division_id: ID!
  file_type: String!
  path: String!
}

type Division {
  division_id: ID!
  name: String!
  teams_count: Float!
  short_name: String
  has_flow_chart: Boolean
  qualified_teams: [Team!]!
  matches_time_ranges: [MatchesTimeRange!]!
  media(filterFileTypes: [String!]): [EventMedia!]!
  rounds: [Round!]!
}

type BracketPool {
  flow_chart: String
}

type BracketMatchResultsTeam {
  sets_won: Float
  sets_lost: Float
  points_won: Float
  points_lost: Float
  sets_pct: Float
  matches_lost: Float
  matches_won: Float
  matches_pct: Float
  scores: String
  roster_team_id: Float
}

type BracketMatchSourceTeamItem {
  id: String
  name: String
  seed: Float
  type: Float
  reseedRank: Float
  overallSeed: Float
}

type BracketMatchSourceTeam {
  ref: BracketMatchSourceTeamItem
  team1: BracketMatchSourceTeamItem
  team2: BracketMatchSourceTeamItem
}

type BracketMatchResults {
  set1: String
  set2: String
  set3: String
  set4: String
  set5: String
  winner: String
  team1: BracketMatchResultsTeam
  team2: BracketMatchResultsTeam
}

type BracketMatch {
  show_previously_accepted_bid_team1: String
  show_previously_accepted_bid_team2: String
  court_id: String
  court_name: String
  display_name: String
  date_start: Float
  match_id: String
  match_number: Float
  ref_code: String
  ref_name: String
  ref_roster_id: Float
  scores: String
  team1_code: String
  team1_name: String
  team1_rank: Float
  team1_roster_id: Float
  team1_temp_name: String
  team1_temp_roster_id: String
  team2_name: String
  team2_rank: Float
  team2_roster_id: Float
  team2_temp_name: String
  team2_temp_roster_id: Float
  temp_score: String
  winner: String
  winning_roster_id: Float
  winning_team_id: Float
  winning_team_name: String
  winning_temp_name: String
  results: BracketMatchResults
  source: BracketMatchSourceTeam
}

type EventBracket {
  display_name: String
  division_name: String
  pool: BracketPool
  matches: [BracketMatch!]
}

type CourtMatchesCourtMatchResultsTeam {
  scores: String
}

type CourtMatchesCourtMatchResults {
  set1: String
  set2: String
  team1: CourtMatchesCourtMatchResultsTeam
  team2: CourtMatchesCourtMatchResultsTeam
  winner: String
}

type CourtMatchesCourtMatch {
  match_id: String
  match_name: String
  division_id: Float
  division_name: String
  division_short_name: String
  date_start: Float
  date_end: Float
  secs_finished: Float
  team1_roster_id: Float
  team2_roster_id: Float
  team_1_name: String
  team_2_name: String
  team_ref_name: String
  color: String
  results: CourtMatchesCourtMatchResults
}

type CourtMatchesCourt {
  court_id: String
  court_name: String
  event_id: Float
  short_name: String
  matches: [CourtMatchesCourtMatch!]
}

type CourtMatchesTime {
  time: Float
  time12: String
  default: String
}

type CourtMatchesDivision {
  division_id: Float
  division_name: String
  gender: String
  sort_order: Float
  max_age: Float
  level_sort_order: Float
  level: String
}

type CourtGrid {
  courts: [CourtMatchesCourt!]
  hours: [CourtMatchesTime!]
  divisions: [CourtMatchesDivision!]
}

type DivisionPoolTeamInfo {
  title: String
  heading: String
  seed_current: Float
  seed_original: Float
  heading_sort_priority: Float
}

type DivisionPoolTeam {
  opponent_team_name: String
  opponent_organization_code: String
  opponent_team_id: Float
  rank: Float
  is_empty_team_pb_stats: Boolean
  matches_won: String
  matches_lost: String
  sets_won: String
  sets_lost: String
  points_ratio: Float
  sets_pct: Float
  info: DivisionPoolTeamInfo!
}

type DivisionPool {
  r_uuid: String
  r_sort_priority: Float
  sort_priority: Float
  rr_sort_priority: Float
  r_name: String
  r_short_name: String
  is_pool: Float
  pb_name: String
  pb_short_name: String
  display_name: String
  team_count: Float
  uuid: String
  rr_name: String
  rr_short_name: String
  division_short_name: String
  is_empty_pb_stats: Boolean
  date_start: Float
  court_start: String
  settings: PoolOrBracketSettings
  teams: [DivisionPoolTeam!]
}

type EventAthlete {
  division_id: Float
  roster_team_id: Float
  first: String
  last: String
  team_name: String
  team_organization_code: String
  jersey: Float
  club_name: String!
  gender: String
  age: Int
}

type EventDivision {
  event_id: ID
  name: String
  short_name: String
  teams_count: Float
  division_id: Float
  gender: String
}

type TeamList {
  division_name: String
  division_short_name: String
  club_name: String
  state: String
  team_name: String
  club_state: String
  organization_code: String
  roster_team_id: Float
  roster_club_id: Float
  division_id: Float
  gender: String
}

type EventLocation {
  location_name: String
  address: String
  city: String
  state: String
  zip: String
  number: Float
}

type EventTeamsSettings {
  sort_by: String
  hide_standings: Boolean
  manual_club_names: Boolean
  manual_teams_addition: Boolean
  baller_tv_available: Boolean
}

type Event {
  hide_seeds: Boolean
  has_rosters: Boolean
  event_id: ID
  modified: DateTime
  id: Float
  name: String
  long_name: String
  event_notes: String
  address: String
  small_logo: String
  city: String
  is_with_prev_qual: Boolean
  date_start: String
  state: String
  schedule_published: Boolean
  tickets_published: Boolean
  is_require_recipient_name_for_each_ticket: Boolean
  tickets_code: String
  has_officials: Boolean
  divisions: [EventDivision!]!
  clubs: [Club!]!
  teams: [TeamList!]!
  athletes: [EventAthlete!]!
  days: [String!]
  locations: [EventLocation!]
  teams_settings: EventTeamsSettings
  sport_sanctioning: String
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

type FeatureMatches {
  match_id: String
  division_id: String
  team1_id: String
  team1_name: String
  team2_id: String
  team2_name: String
  court_name: String
  court_short_name: String
  match_start: String
}

type PaginatedEvents {
  items: [Event!]!
  page_info: PageInfo!
}

"""Generate a link to the next pool"""
type NextPrevPool {
  is_pool: Float
  name: String
  uuid: String
}

type PoolResultsResultsTeam {
  heading: String
  heading_sort: Float
  matches_lost: Float
  matches_pct: Float
  matches_won: Float
  overallSeed: Float
  points_lost: Float
  points_ratio: Float
  points_won: Float
  roster_team_id: Float
  scores: String
  sets_lost: Float
  sets_pct: Float
  sets_won: Float
  title: String
}

type PoolResultsResults {
  set1: String
  set2: String
  set3: String
  set4: String
  set5: String
  team1: PoolResultsResultsTeam
  team2: PoolResultsResultsTeam
}

type PoolResults {
  court_name: String
  date_start: String
  display_name: String
  division_short_name: String
  match_id: String
  ref_roster_id: String
  ref_team_code: String
  ref_team_name: String
  team1_code: String
  team1_name: String
  team1_roster_id: Float
  team1_master_id: Float
  team2_code: String
  team2_name: String
  team2_roster_id: Float
  team2_master_id: Float
  results: PoolResultsResults!
}

type PoolStandingsStats {
  name: String
  rank: String
  team_id: String
  sets_pct: Float
  sets_won: Float
  sets_lost: Float
  points_won: Float
  matches_pct: Float
  matches_won: Float
  points_lost: Float
  matches_lost: Float
  points_ratio: Float
}

type PoolStandings {
  display_name: String
  division_name: String
  division_short_name: String
  pb_stats: [PoolStandingsStats!]!
}

type PoolFuture {
  team_ids: [String!]!
  teams: [TeamAdvancement!]!
}

type PoolUpcomingMatches {
  court_name: String
  date_start: Float
  display_name: String
  division_short_name: String
  footnote_play: String
  footnote_team1: String
  footnote_team2: String
  match_id: String
  ref_roster_id: String
  ref_team_code: String
  ref_team_name: String
  results: String
  team1_code: String
  team1_name: String
  team1_roster_id: String
  team1_master_id: String
  team2_code: String
  team2_name: String
  team2_roster_id: String
  team2_master_id: String
}

type EventPool {
  event_id: Float
  uuid: String
  r_uuid: String
  division_short_name: String
  division_id: String
  display_name: String
  display_name_short: String
  team_count: Float
  is_pool: Boolean
  next: NextPrevPool
  prev: NextPrevPool
  results: [PoolResults!]
  standings: [PoolStandings!]
  pb_finishes: PoolFuture
  upcoming_matches: [PoolUpcomingMatches!]
  settings: PoolOrBracketSettings
}

type StandingInfo {
  points: Float
}

type TeamDetails {
  roster_team_id: Float
  division_id: Float
  seed_current: Float
  team_name: String
  division_short_name: String
  organization_code: String
  points_ratio: Float
  matches_won: Float
  matches_lost: Float
  sets_won: Float
  sets_lost: Float
  sets_pct: Float
  rank: Float
  info: StandingInfo
}

type Standing {
  teams: [TeamDetails!]!
  heading_group: String!
}

type EventDivisionDetails {
  standing: [Standing!]!
}

type EventUpcoming {
  match_type: String
  match_id: String
  unix_finished: String
  results: String
  display_name: String
  date_start_formatted: String
  date_start: String
  division_id: String
  division_short_name: String
  court_name: String
  pool_name: String
  pool_bracket_id: String
  is_pool: Float
  opponent_team_name: String
  opponent_organization_code: String
  pb_name: String
  round_name: String
  footnote_play: String
  footnote_team1: String
  footnote_team2: String
  settings: PoolOrBracketSettings
}

type TeamSingleStaff {
  first: String
  last: String
  sort_order: Float
  role_name: String
}

type TeamSingleAthlete {
  first: String
  last: String
  uniform: Float
  short_position: String
  gradyear: Float
  height: String
}

type TeamSingleMatchResultsTeam {
  heading: String
  heading_sort: Float
  matches_lost: Float
  matches_pct: Float
  matches_won: Float
  overallSeed: Float
  points_lost: Float
  points_ratio: Float
  points_won: Float
  roster_team_id: Float
  scores: String
  sets_lost: Float
  sets_pct: Float
  sets_won: Float
  title: String
}

type TeamSingleMatchResults {
  set1: String
  set2: String
  set3: String
  set4: String
  set5: String
  winner: String
  team1: TeamSingleMatchResultsTeam!
  team2: TeamSingleMatchResultsTeam!
}

type TeamSingleMatch {
  date_start: Float
  date_start_formatted: String
  display_name: String
  division_id: Float
  division_short_name: String
  match_id: String
  match_type: String
  opponent_organization_code: String
  opponent_team_id: Float
  opponent_team_name: String
  pool_bracket_id: String
  unix_finished: Float
  results: TeamSingleMatchResults
}

type TeamSingleResults {
  uuid: String
  is_pool: Float
  pb_name: String
  round_name: String
  sort_priority: Float
  pb_stats: [PoolStandingsStats!]
  matches: [TeamSingleMatch!]!
}

type TeamSinglePbInfo {
  uuid: String
  name: String
  is_pool: Float
  pb_finishes: PoolFuture
}

type TeamSingle {
  roster_team_id: Float
  master_team_id: Float
  division_id: Float
  team_name: String
  matches_won: Float
  matches_lost: Float
  manual_club_name: String
  sets_won: Float
  sets_lost: Float
  organization_code: String
  points_won: Float
  points_lost: Float
  roster_club_id: Float
  club_name: String
  state: String
  athletes: [TeamSingleAthlete!]
  staff: [TeamSingleStaff!]
  results: [TeamSingleResults!]
  upcoming: [EventUpcoming!]
  pb_info: [TeamSinglePbInfo!]
  bracket_finishes: MatchFinishes
}

type UpcomingMatches {
  division_id: Float
  division_name: String
  club_id: Float
  club_name: String
  club_state: String
  club_code: String
  team_code: String
  team_id: Float
  team_name: String
  opponent_id: Float
  opponent_name: String
  secs_start: Float
  secs_finished: Float
  court_name: String
  matches_won: Float
  matches_lost: Float
  sets_won: Float
  sets_lost: Float
  sets_pct: Float
  seed: Float
}

type EventOfficialSchedule {
  schedule_id: ID!
  event_official_id: ID!
  event_official_group_id: ID
  division_id: ID!
  match_id: ID!
  match_name: String!
  match_start_time: Float
  published: Boolean!
  court_id: ID
  match: Match
}

type UserShortInfo {
  user_id: ID!
  first: String
  last: String
}

type OfficialShortInfo {
  official_id: ID!
  user_id: ID!
  rank: String
}

type EventOfficialExternal {
  official_info: OfficialShortInfo!
  user_info: UserShortInfo!
}

type EventOfficial {
  event_official_id: ID!
  official_id: ID!
  schedule_name: String
  additional_restrictions: String
  departure_time: String
  official_additional_role: String
  external: EventOfficialExternal!
  schedules: [EventOfficialSchedule!]!
}

type MatchReferenceExternal {
  pool_bracket_info: PoolOrBracketShortInfo
}

type EventTeamRosterAthlete {
  id: ID!
  first: String
  last: String
  short_position: String
  uniform: Int
  gradyear: Int
}

type EventTeamRosterStaff {
  id: ID!
  first: String
  last: String
  role_name: String
  sort_order: Int
}

type EventTeamRoster {
  event_id: ID!
  event_name: String
  state: String
  club_name: String
  organization_code: String
  team_name: String
  roster_team_id: String
  roster_athletes: [EventTeamRosterAthlete!]
  roster_staff: [EventTeamRosterStaff!]
}

type Staff {
  staff_id: ID!
  first: String!
  last: String!
  club_name: String!
  team_id: ID!
  team_name: String!
  role_name: String
  organization_code: String
  state: String
}

type PaginatedStaff {
  items: [Staff!]!
  page_info: PageInfo!
}

type Query {
  paginatedClubs(page: Float!, pageSize: Float!, eventKey: ID!, search: String): PaginatedClubs!
  favoriteTeams(eventKey: ID!, teamsIds: [ID!]!, search: String): [Team!]!
  qualifiedTeams(eventKey: ID!): [Team!]!
  divisionTeamsStanding(eventKey: ID!, divisionId: ID!): [Team!]!
  paginatedDivisionTeams(page: Float!, pageSize: Float!, eventKey: ID!, divisionId: ID!, search: String): PaginatedTeams!
  paginatedAthletes(page: Float!, pageSize: Float!, eventKey: ID!, search: String): PaginatedAthletes!
  dayRangeMatches(eventKey: ID!, divisionId: ID, after: String!, before: String!): FilteredMatches!
  divisionPoolBrackets(eventKey: ID!, divisionId: ID!): [PoolOrBracket!]!
  paginatedEvents(page: Float!, pageSize: Float!, search: String, startBefore: String, startAfter: String, endBefore: String, endAfter: String, years: [String!], asc: Boolean): PaginatedEvents!
  event(id: ID!): Event
  divisionDetails(id: ID!, divisionIds: [ID!]!): EventDivisionDetails
  pools(id: ID!, divisionId: ID!): [DivisionPool!]!
  pool(id: ID!, poolId: ID!): EventPool!
  teamSingle(id: ID!, teamId: ID!): TeamSingle!
  courtMatches(id: ID!, day: String!, hour: String!, hours: String!, division: String!): CourtGrid!
  featureMatches(id: ID!): [FeatureMatches!]!
  poolIdByTeamId(id: ID!, teamId: ID!, poolId: ID): EventPool
  upcomingMatches(id: ID!): [UpcomingMatches!]
  bracket(id: ID!, poolId: String!): EventBracket
  roster(id: ID!): [EventTeamRoster!]!
  paginatedStaff(page: Float!, pageSize: Float!, eventKey: ID!, search: String): PaginatedStaff!
  divisions(eventKey: ID!): [Division!]!
  divisionRounds(eventKey: ID!, divisionId: ID!): [Round!]!
  courts(eventKey: ID!): [Court!]!
  relatedCourts(uniqKey: String): [Court!]!
  eventOfficials(eventKey: ID!): [EventOfficial!]!
  eventOfficialsSchedules(eventKey: ID!): [EventOfficialSchedule!]!
}